import { Metadata } from 'next'
// TODO: wr<PERSON><PERSON><PERSON> po buildzie
// import FAQSection from '@/components/faq/FAQSection'
import FAQHero from '@/components/faq/FAQHero'
import FAQContact from '@/components/faq/FAQContact'

export const metadata: Metadata = {
  title: 'FAQ - Często zadawane pytania | SOLVICTUS',
  description: 'Odpowiedzi na najczęściej zadawane pytania dotyczące usług sprzątania po tragedii, procedur, kosztów i certyfikatów SOLVICTUS.',
  keywords: 'FAQ, cz<PERSON>sto zadawane pytania, sprzątanie po zgonie, koszty dezynfekcji, procedury, certyfikaty',
}

export default function FAQPage() {
  return (
    <>
      <FAQHero />
      {/* TODO: wrócić po buildzie */}
      {/* <FAQSection /> */}
      <FAQContact />
    </>
  )
}
