import { notFound } from 'next/navigation'
import Link from 'next/link'
import { Calendar, Clock, User, ArrowLeft, Share2, Eye } from 'lucide-react'
import { Metadata } from 'next'

// Mock data - w prawdziwej aplikacji pobierałbyś to z API/bazy danych
const articles = [
  {
    id: '1',
    title: 'Jak bezpiecznie sprzątać po zgonie? Kompletny przewodnik',
    content: `
      <h2>Wprowadzenie</h2>
      <p>Sprzątanie po zgonie to jedna z najtrudniejszych sytuacji, z jakimi może się zmierzyć człowiek. Wymaga nie tylko odpowiedniego przygotowania technicznego, ale także emocjonalnego podejścia do tej delikatnej kwestii.</p>
      
      <h2>Bezpieczeństwo przede wszystkim</h2>
      <p>Przed rozpoczęciem jakich<PERSON><PERSON>wi<PERSON> prac sprzątających należy zad<PERSON>ć o odpowiednie środki ochrony osobistej:</p>
      <ul>
        <li>Kombinezony ochronne jednorazowego użytku</li>
        <li>Rękawice nitrylowe (podwójna warstwa)</li>
        <li>Maski respiratorowe FFP3</li>
        <li>Okulary ochronne</li>
        <li>Buty ochronne lub ochraniacze na buty</li>
      </ul>
      
      <h2>Etapy sprzątania</h2>
      <h3>1. Ocena sytuacji</h3>
      <p>Pierwszym krokiem jest dokładna ocena miejsca zdarzenia. Należy określić:</p>
      <ul>
        <li>Zakres skażenia biologicznego</li>
        <li>Rodzaje powierzchni do oczyszczenia</li>
        <li>Potrzebne środki i sprzęt</li>
        <li>Czas potrzebny na wykonanie prac</li>
      </ul>
      
      <h3>2. Usuwanie widocznych zanieczyszczeń</h3>
      <p>Wszystkie widoczne materiały biologiczne muszą zostać usunięte przy użyciu odpowiednich narzędzi i środków absorbujących.</p>
      
      <h3>3. Dezynfekcja powierzchni</h3>
      <p>Po usunięciu zanieczyszczeń należy przeprowadzić dokładną dezynfekcję wszystkich powierzchni środkami o potwierdzonej skuteczności przeciwko patogenom.</p>
      
      <h3>4. Ozonowanie</h3>
      <p>Ostatnim etapem jest ozonowanie pomieszczenia w celu neutralizacji zapachów i dodatkowej dezynfekcji powietrza.</p>
      
      <h2>Utylizacja odpadów</h2>
      <p>Wszystkie odpady powstałe podczas sprzątania muszą być traktowane jako odpady medyczne i utylizowane zgodnie z obowiązującymi przepisami.</p>
      
      <h2>Kiedy wezwać profesjonalistów?</h2>
      <p>Sprzątanie po zgonie zawsze powinno być powierzone profesjonalnej firmie. SOLVICTUS dysponuje odpowiednim sprzętem, środkami i doświadczeniem, aby przeprowadzić te prace bezpiecznie i skutecznie.</p>
      
      <h2>Wsparcie psychologiczne</h2>
      <p>Pamiętaj, że w tak trudnych chwilach ważne jest także wsparcie psychologiczne. Nie wahaj się skorzystać z pomocy specjalistów.</p>
    `,
    excerpt: 'Szczegółowy przewodnik krok po kroku dotyczący bezpiecznego sprzątania po zgonie. Poznaj procedury, środki ochrony i najważniejsze zasady.',
    category: 'Sprzątanie po zgonach',
    categoryColor: 'text-red-600 bg-red-50',
    author: 'Dr Marek Kowalski',
    date: '2024-01-15',
    readTime: '8 min',
    views: 1250,
    featured: true,
    image: '/blog/sprzatanie-po-zgonie.jpg'
  },
  {
    id: '2',
    title: 'Ozonowanie - skuteczna metoda neutralizacji zapachów',
    content: `
      <h2>Co to jest ozonowanie?</h2>
      <p>Ozonowanie to proces wykorzystujący ozon (O₃) do neutralizacji zapachów, dezynfekcji powietrza i powierzchni. Jest to jedna z najskuteczniejszych metod usuwania nieprzyjemnych zapachów.</p>
      
      <h2>Jak działa ozon?</h2>
      <p>Ozon jest silnym utleniaczem, który:</p>
      <ul>
        <li>Niszczy cząsteczki odpowiedzialne za nieprzyjemne zapachy</li>
        <li>Eliminuje bakterie, wirusy i grzyby</li>
        <li>Penetruje do trudno dostępnych miejsc</li>
        <li>Rozkłada się naturalnie do tlenu</li>
      </ul>
      
      <h2>Zastosowania ozonowania</h2>
      <h3>W domu:</h3>
      <ul>
        <li>Usuwanie zapachów po pożarze</li>
        <li>Neutralizacja zapachów po zalaniu</li>
        <li>Eliminacja zapachów zwierzęcych</li>
        <li>Odświeżanie pomieszczeń po remoncie</li>
      </ul>
      
      <h3>W biznesie:</h3>
      <ul>
        <li>Hotele i restauracje</li>
        <li>Biura i przestrzenie komercyjne</li>
        <li>Pojazdy (samochody, autobusy)</li>
        <li>Magazyny i hale produkcyjne</li>
      </ul>
      
      <h2>Proces ozonowania</h2>
      <p>Profesjonalne ozonowanie składa się z kilku etapów:</p>
      <ol>
        <li><strong>Przygotowanie pomieszczenia</strong> - usunięcie osób, zwierząt i roślin</li>
        <li><strong>Ustawienie generatora ozonu</strong> - dobór odpowiedniej mocy</li>
        <li><strong>Proces ozonowania</strong> - czas trwania zależy od wielkości pomieszczenia</li>
        <li><strong>Wietrzenie</strong> - usunięcie pozostałości ozonu</li>
        <li><strong>Kontrola jakości</strong> - sprawdzenie skuteczności procesu</li>
      </ol>
      
      <h2>Bezpieczeństwo</h2>
      <p>Ozonowanie musi być przeprowadzane przez profesjonalistów, ponieważ:</p>
      <ul>
        <li>Ozon w wysokich stężeniach jest szkodliwy dla zdrowia</li>
        <li>Wymaga specjalistycznego sprzętu</li>
        <li>Proces musi być odpowiednio kontrolowany</li>
        <li>Konieczne jest właściwe wietrzenie po zabiegu</li>
      </ul>
      
      <h2>Dlaczego SOLVICTUS?</h2>
      <p>Nasze doświadczenie w ozonowaniu obejmuje:</p>
      <ul>
        <li>Profesjonalny sprzęt najnowszej generacji</li>
        <li>Certyfikowanych specjalistów</li>
        <li>Indywidualne podejście do każdego przypadku</li>
        <li>Gwarancję skuteczności</li>
      </ul>
    `,
    excerpt: 'Dowiedz się, jak działa ozonowanie, jakie ma zastosowania i dlaczego jest tak skuteczne w usuwaniu nieprzyjemnych zapachów.',
    category: 'Ozonowanie',
    categoryColor: 'text-cyan-600 bg-cyan-50',
    author: 'Mgr Anna Nowak',
    date: '2024-01-10',
    readTime: '6 min',
    views: 890,
    featured: false,
    image: '/blog/ozonowanie.jpg'
  }
  // Dodaj więcej artykułów...
]

interface PageProps {
  params: {
    id: string
  }
}

// Generate static params for all articles
export async function generateStaticParams() {
  return articles.map((article) => ({
    id: article.id,
  }))
}

// Generate metadata for each article
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const article = articles.find(a => a.id === params.id)

  if (!article) {
    return {
      title: 'Artykuł nie znaleziony - SOLVICTUS Blog',
      description: 'Szukany artykuł nie został znaleziony.'
    }
  }

  return {
    title: `${article.title} - SOLVICTUS Blog`,
    description: article.excerpt,
    keywords: `${article.category}, SOLVICTUS, blog, poradniki, ${article.title}`,
    openGraph: {
      title: article.title,
      description: article.excerpt,
      type: 'article',
      publishedTime: article.date,
      authors: [article.author],
    },
    twitter: {
      card: 'summary_large_image',
      title: article.title,
      description: article.excerpt,
    },
  }
}

export default function BlogArticlePage({ params }: PageProps) {
  const article = articles.find(a => a.id === params.id)

  if (!article) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Link
            href="/blog"
            className="inline-flex items-center space-x-2 text-primary-600 hover:text-primary-700 mb-6"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Powrót do bloga</span>
          </Link>

          <div className="mb-6">
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${article.categoryColor}`}>
              {article.category}
            </span>
          </div>

          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6 leading-tight">
            {article.title}
          </h1>

          <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 mb-6">
            <div className="flex items-center space-x-2">
              <User className="w-4 h-4" />
              <span>{article.author}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="w-4 h-4" />
              <span>{new Date(article.date).toLocaleDateString('pl-PL')}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="w-4 h-4" />
              <span>{article.readTime}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Eye className="w-4 h-4" />
              <span>{article.views} wyświetleń</span>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <button className="flex items-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors">
              <Share2 className="w-4 h-4" />
              <span>Udostępnij</span>
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          {/* Featured image placeholder */}
          <div className="h-64 md:h-96 bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center">
            <span className="text-primary-600">Zdjęcie artykułu</span>
          </div>

          {/* Article content */}
          <div className="p-8 md:p-12">
            <div 
              className="prose prose-lg max-w-none"
              dangerouslySetInnerHTML={{ __html: article.content }}
            />
          </div>
        </div>

        {/* Related articles */}
        <div className="mt-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">
            Powiązane artykuły
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {articles
              .filter(a => a.id !== article.id && a.category === article.category)
              .slice(0, 2)
              .map((relatedArticle) => (
                <Link
                  key={relatedArticle.id}
                  href={`/blog/${relatedArticle.id}`}
                  className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
                >
                  <div className="h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                    <span className="text-gray-500 text-sm">Zdjęcie artykułu</span>
                  </div>
                  <div className="p-6">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${relatedArticle.categoryColor}`}>
                      {relatedArticle.category}
                    </span>
                    <h4 className="text-lg font-bold text-gray-900 mt-3 mb-2">
                      {relatedArticle.title}
                    </h4>
                    <p className="text-gray-600 text-sm">
                      {relatedArticle.excerpt}
                    </p>
                  </div>
                </Link>
              ))}
          </div>
        </div>
      </div>
    </div>
  )
}
