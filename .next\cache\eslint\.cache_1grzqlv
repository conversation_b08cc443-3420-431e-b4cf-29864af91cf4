[{"C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\blog\\layout.tsx": "1", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\blog\\page.tsx": "2", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\blog\\[id]\\page.tsx": "3", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\cennik\\page.tsx": "4", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\cookies\\page.tsx": "5", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\dla-firm\\page.tsx": "6", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\faq\\page.tsx": "7", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\galeria\\page.tsx": "8", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\kontakt\\page.tsx": "9", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\layout.tsx": "10", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\o-nas\\page.tsx": "11", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\opinie\\page.tsx": "12", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\page.tsx": "13", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\polityka-prywatnosci\\page.tsx": "14", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\regulamin\\page.tsx": "15", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\robots.ts": "16", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\rodo\\page.tsx": "17", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\sitemap.ts": "18", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\cennik\\page.tsx": "19", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\dezynfekcja-po-smierci\\page.tsx": "20", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\ozonowanie\\page.tsx": "21", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\ozonowanie-pomieszczen\\page.tsx": "22", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\page.tsx": "23", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\po-pozarach\\page.tsx": "24", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\po-zalaniach\\page.tsx": "25", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\sprzatanie-po-pozarach\\page.tsx": "26", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\sprzatanie-po-zgonach\\page.tsx": "27", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\usuwanie-skutkow-powodzi\\page.tsx": "28", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\usuwanie-zapachow\\page.tsx": "29", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\AboutHero.tsx": "30", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\CompanyHistory.tsx": "31", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\MissionValues.tsx": "32", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\TeamSection.tsx": "33", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BContact.tsx": "34", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BHero.tsx": "35", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BPartners.tsx": "36", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BProcess.tsx": "37", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BServices.tsx": "38", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogCategories.tsx": "39", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogGrid.tsx": "40", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogHero.tsx": "41", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogNewsletter.tsx": "42", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\CertificationsSection.tsx": "43", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Chatbot.tsx": "44", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactForm.tsx": "45", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactHero.tsx": "46", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactInfo.tsx": "47", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactMap.tsx": "48", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\EmergencyContact.tsx": "49", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\faq\\FAQContact.tsx": "50", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\faq\\FAQHero.tsx": "51", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\faq\\FAQSection.tsx": "52", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Footer.tsx": "53", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryFilters.tsx": "54", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryGrid.tsx": "55", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryHero.tsx": "56", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryStats.tsx": "57", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Header.tsx": "58", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\HeroSection.tsx": "59", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\LanguageSwitcher.tsx": "60", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingCalculator.tsx": "61", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingFactors.tsx": "62", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingFAQ.tsx": "63", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingHero.tsx": "64", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingTables.tsx": "65", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsFilters.tsx": "66", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsForm.tsx": "67", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsGrid.tsx": "68", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsHero.tsx": "69", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsStats.tsx": "70", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\SEOSchema.tsx": "71", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\ServicesOverview.tsx": "72", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\TestimonialsPreview.tsx": "73", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\WhyChooseUs.tsx": "74", "C:\\Users\\<USER>\\Downloads\\test2\\src\\hooks\\useTranslation.ts": "75", "C:\\Users\\<USER>\\Downloads\\test2\\src\\lib\\i18n.ts": "76"}, {"size": 1021, "mtime": 1752337941058, "results": "77", "hashOfConfig": "78"}, {"size": 916, "mtime": 1752342986430, "results": "79", "hashOfConfig": "78"}, {"size": 11520, "mtime": 1752337573823, "results": "80", "hashOfConfig": "78"}, {"size": 1012, "mtime": 1752343075532, "results": "81", "hashOfConfig": "78"}, {"size": 11214, "mtime": 1752343507810, "results": "82", "hashOfConfig": "78"}, {"size": 945, "mtime": 1752343198071, "results": "83", "hashOfConfig": "78"}, {"size": 754, "mtime": 1752343158594, "results": "84", "hashOfConfig": "78"}, {"size": 989, "mtime": 1752343016811, "results": "85", "hashOfConfig": "78"}, {"size": 1034, "mtime": 1752342969661, "results": "86", "hashOfConfig": "78"}, {"size": 2654, "mtime": 1752342942284, "results": "87", "hashOfConfig": "78"}, {"size": 939, "mtime": 1751941603543, "results": "88", "hashOfConfig": "78"}, {"size": 1027, "mtime": 1752343124444, "results": "89", "hashOfConfig": "78"}, {"size": 1734, "mtime": 1752337646107, "results": "90", "hashOfConfig": "78"}, {"size": 7689, "mtime": 1752337983065, "results": "91", "hashOfConfig": "78"}, {"size": 9316, "mtime": 1751976470034, "results": "92", "hashOfConfig": "78"}, {"size": 270, "mtime": 1751942136665, "results": "93", "hashOfConfig": "78"}, {"size": 10397, "mtime": 1751976601627, "results": "94", "hashOfConfig": "78"}, {"size": 2582, "mtime": 1752337758598, "results": "95", "hashOfConfig": "78"}, {"size": 957, "mtime": 1752343100138, "results": "96", "hashOfConfig": "78"}, {"size": 13693, "mtime": 1752277452957, "results": "97", "hashOfConfig": "78"}, {"size": 18697, "mtime": 1752273055949, "results": "98", "hashOfConfig": "78"}, {"size": 18767, "mtime": 1752277672145, "results": "99", "hashOfConfig": "78"}, {"size": 11256, "mtime": 1751941771535, "results": "100", "hashOfConfig": "78"}, {"size": 13961, "mtime": 1752272924211, "results": "101", "hashOfConfig": "78"}, {"size": 15294, "mtime": 1752272985335, "results": "102", "hashOfConfig": "78"}, {"size": 13960, "mtime": 1752277517822, "results": "103", "hashOfConfig": "78"}, {"size": 11454, "mtime": 1752272869769, "results": "104", "hashOfConfig": "78"}, {"size": 16922, "mtime": 1752277590251, "results": "105", "hashOfConfig": "78"}, {"size": 20150, "mtime": 1752277761373, "results": "106", "hashOfConfig": "78"}, {"size": 3395, "mtime": 1751941621472, "results": "107", "hashOfConfig": "78"}, {"size": 5945, "mtime": 1751941679427, "results": "108", "hashOfConfig": "78"}, {"size": 6136, "mtime": 1752338117507, "results": "109", "hashOfConfig": "78"}, {"size": 8549, "mtime": 1751941715616, "results": "110", "hashOfConfig": "78"}, {"size": 14933, "mtime": 1751976546482, "results": "111", "hashOfConfig": "78"}, {"size": 3654, "mtime": 1751942061000, "results": "112", "hashOfConfig": "78"}, {"size": 7130, "mtime": 1752338177653, "results": "113", "hashOfConfig": "78"}, {"size": 8918, "mtime": 1751948759624, "results": "114", "hashOfConfig": "78"}, {"size": 9979, "mtime": 1751976508362, "results": "115", "hashOfConfig": "78"}, {"size": 3928, "mtime": 1751976268501, "results": "116", "hashOfConfig": "78"}, {"size": 10456, "mtime": 1751976300764, "results": "117", "hashOfConfig": "78"}, {"size": 2351, "mtime": 1752337785604, "results": "118", "hashOfConfig": "78"}, {"size": 4912, "mtime": 1751958115442, "results": "119", "hashOfConfig": "78"}, {"size": 8058, "mtime": 1751941525567, "results": "120", "hashOfConfig": "78"}, {"size": 12063, "mtime": 1751960781951, "results": "121", "hashOfConfig": "78"}, {"size": 9805, "mtime": 1751941951409, "results": "122", "hashOfConfig": "78"}, {"size": 3310, "mtime": 1751941905607, "results": "123", "hashOfConfig": "78"}, {"size": 8537, "mtime": 1751941986115, "results": "124", "hashOfConfig": "78"}, {"size": 10943, "mtime": 1752338264260, "results": "125", "hashOfConfig": "78"}, {"size": 7815, "mtime": 1751941588425, "results": "126", "hashOfConfig": "78"}, {"size": 6381, "mtime": 1751941869753, "results": "127", "hashOfConfig": "78"}, {"size": 1838, "mtime": 1751941799191, "results": "128", "hashOfConfig": "78"}, {"size": 8040, "mtime": 1751941841186, "results": "129", "hashOfConfig": "78"}, {"size": 6429, "mtime": 1752277832116, "results": "130", "hashOfConfig": "78"}, {"size": 5095, "mtime": 1751976140487, "results": "131", "hashOfConfig": "78"}, {"size": 14837, "mtime": 1751976210767, "results": "132", "hashOfConfig": "78"}, {"size": 2465, "mtime": 1751958172121, "results": "133", "hashOfConfig": "78"}, {"size": 3804, "mtime": 1751958304390, "results": "134", "hashOfConfig": "78"}, {"size": 9853, "mtime": 1752277388184, "results": "135", "hashOfConfig": "78"}, {"size": 5070, "mtime": 1752337473098, "results": "136", "hashOfConfig": "78"}, {"size": 2515, "mtime": 1751962300012, "results": "137", "hashOfConfig": "78"}, {"size": 14364, "mtime": 1751960513747, "results": "138", "hashOfConfig": "78"}, {"size": 5045, "mtime": 1751976088545, "results": "139", "hashOfConfig": "78"}, {"size": 6103, "mtime": 1751976124112, "results": "140", "hashOfConfig": "78"}, {"size": 3125, "mtime": 1751958362870, "results": "141", "hashOfConfig": "78"}, {"size": 10513, "mtime": 1751960452647, "results": "142", "hashOfConfig": "78"}, {"size": 6971, "mtime": 1751961369388, "results": "143", "hashOfConfig": "78"}, {"size": 10462, "mtime": 1751961428706, "results": "144", "hashOfConfig": "78"}, {"size": 9199, "mtime": 1752338313125, "results": "145", "hashOfConfig": "78"}, {"size": 3027, "mtime": 1751960660134, "results": "146", "hashOfConfig": "78"}, {"size": 4994, "mtime": 1751961317897, "results": "147", "hashOfConfig": "78"}, {"size": 3539, "mtime": 1751942164899, "results": "148", "hashOfConfig": "78"}, {"size": 6149, "mtime": 1751941457132, "results": "149", "hashOfConfig": "78"}, {"size": 6601, "mtime": 1752338213844, "results": "150", "hashOfConfig": "78"}, {"size": 6256, "mtime": 1751941488513, "results": "151", "hashOfConfig": "78"}, {"size": 1075, "mtime": 1751962330282, "results": "152", "hashOfConfig": "78"}, {"size": 4731, "mtime": 1751960879677, "results": "153", "hashOfConfig": "78"}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "fddo2y", {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\blog\\layout.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\blog\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\blog\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\cennik\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\cookies\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\dla-firm\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\faq\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\galeria\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\kontakt\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\o-nas\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\opinie\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\polityka-prywatnosci\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\regulamin\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\robots.ts", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\rodo\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\sitemap.ts", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\cennik\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\dezynfekcja-po-smierci\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\ozonowanie\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\ozonowanie-pomieszczen\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\po-pozarach\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\po-zalaniach\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\sprzatanie-po-pozarach\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\sprzatanie-po-zgonach\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\usuwanie-skutkow-powodzi\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\usuwanie-zapachow\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\AboutHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\CompanyHistory.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\MissionValues.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\TeamSection.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BContact.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BPartners.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BProcess.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BServices.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogCategories.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogGrid.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogNewsletter.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\CertificationsSection.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Chatbot.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactForm.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactInfo.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactMap.tsx", ["382"], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\EmergencyContact.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\faq\\FAQContact.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\faq\\FAQHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\faq\\FAQSection.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryFilters.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryGrid.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryStats.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\HeroSection.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\LanguageSwitcher.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingCalculator.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingFactors.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingFAQ.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingTables.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsFilters.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsForm.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsGrid.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsStats.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\SEOSchema.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\ServicesOverview.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\TestimonialsPreview.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\WhyChooseUs.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\hooks\\useTranslation.ts", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\lib\\i18n.ts", [], [], {"ruleId": "383", "severity": 1, "message": "384", "line": 17, "column": 9, "nodeType": "385", "endLine": 21, "endColumn": 4}, "react-hooks/exhaustive-deps", "The 'companyLocation' object makes the dependencies of useEffect Hook (at line 114) change on every render. To fix this, wrap the initialization of 'companyLocation' in its own useMemo() Hook.", "VariableDeclarator"]