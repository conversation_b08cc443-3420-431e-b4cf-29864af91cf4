"use strict";(()=>{var e={};e.id=717,e.ids=[717],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8008:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>x,patchFetch:()=>q,requestAsyncStorage:()=>f,routeModule:()=>h,serverHooks:()=>w,staticGenerationAsyncStorage:()=>g});var i={};r.r(i),r.d(i,{default:()=>p});var a={};r.r(a),r.d(a,{GET:()=>m});var o=r(9303),n=r(8716),s=r(3131),l=r(5661);function p(){let e="https://solvictus.pl",t=new Date,r=[{url:e,lastModified:t,changeFrequency:"weekly",priority:1},{url:`${e}/o-nas`,lastModified:t,changeFrequency:"monthly",priority:.8},{url:`${e}/uslugi`,lastModified:t,changeFrequency:"weekly",priority:.9},{url:`${e}/kontakt`,lastModified:t,changeFrequency:"monthly",priority:.9},{url:`${e}/dla-firm`,lastModified:t,changeFrequency:"weekly",priority:.8}],i=["sprzatanie-po-zgonach","sprzatanie-po-pozarach","usuwanie-skutkow-powodzi","dezynfekcja-po-smierci","ozonowanie-pomieszczen","usuwanie-zapachow"].map(r=>({url:`${e}/uslugi/${r}`,lastModified:t,changeFrequency:"monthly",priority:.8}));return[...r,...i,...[{path:"blog",priority:.6,frequency:"weekly"},{path:"opinie",priority:.6,frequency:"weekly"},{path:"galeria",priority:.5,frequency:"monthly"},{path:"faq",priority:.7,frequency:"monthly"},{path:"cennik",priority:.7,frequency:"monthly"}].map(r=>({url:`${e}/${r.path}`,lastModified:t,changeFrequency:r.frequency,priority:r.priority})),...["polityka-prywatnosci","regulamin","rodo","cookies"].map(r=>({url:`${e}/${r}`,lastModified:t,changeFrequency:"yearly",priority:.3})),...[{id:"1",lastModified:new Date("2024-01-15")},{id:"2",lastModified:new Date("2024-01-10")}].map(t=>({url:`${e}/blog/${t.id}`,lastModified:t.lastModified,changeFrequency:"monthly",priority:.5}))]}var u=r(707);let d={...i},c=d.default,y=d.generateSitemaps;if("function"!=typeof c)throw Error('Default export is missing in "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\sitemap.ts"');async function m(e,t){let r;let{__metadata_id__:i,...a}=t.params||{},o=y?await y():null;if(o&&null==(r=o.find(e=>{let t=e.id.toString();return(t+=".xml")===i})?.id))return new l.NextResponse("Not Found",{status:404});let n=await c({id:r}),s=(0,u.resolveRouteData)(n,"sitemap");return new l.NextResponse(s,{headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=0, must-revalidate"}})}let h=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/sitemap.xml/route",pathname:"/sitemap.xml",filename:"sitemap",bundlePath:"app/sitemap.xml/route"},resolvedPagePath:"next-metadata-route-loader?page=%2Fsitemap.xml%2Froute&filePath=C%3A%5CUsers%5Cmariu%5CDownloads%5Ctest2%5Csrc%5Capp%5Csitemap.ts&isDynamic=1!?__next_metadata_route__",nextConfigOutput:"",userland:a}),{requestAsyncStorage:f,staticGenerationAsyncStorage:g,serverHooks:w}=h,x="/sitemap.xml/route";function q(){return(0,s.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:g})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[948,346],()=>r(8008));module.exports=i})();