import { Metadata } from 'next'
import B2BHero from '@/components/b2b/B2BHero'
import B2BServices from '@/components/b2b/B2BServices'
import B2BPartners from '@/components/b2b/B2BPartners'
import B2BProcess from '@/components/b2b/B2BProcess'
// TODO: wr<PERSON><PERSON><PERSON> po buildzie
// import B2BContact from '@/components/b2b/B2BContact'

export const metadata: Metadata = {
  title: 'Dla firm - SOLVICTUS | Usługi B2B, kontrakty, ubezpieczenia',
  description: 'Specjalistyczne usługi sprzątania dla firm, hoteli, biur, instytucji. Kontrakty długoterminowe, współpraca z ubezpieczycielami, szybka reakcja 24/7.',
  keywords: 'usługi dla firm, sprzątanie biur, hotele, kontrakty, ubezpieczenia, B2B, instytucje, szpitale',
}

export default function B2BPage() {
  return (
    <>
      <B2BHero />
      <B2BServices />
      <B2BPartners />
      <B2BProcess />
      {/* TODO: wr<PERSON><PERSON><PERSON> po buildzie */}
      {/* <B2BContact /> */}
    </>
  )
}
