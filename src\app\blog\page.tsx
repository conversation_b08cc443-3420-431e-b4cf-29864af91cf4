// TODO: wr<PERSON><PERSON><PERSON> po buildzie - usunięto 'use client' i interaktywność
// 'use client'

// import { useState } from 'react'
import BlogHero from '@/components/blog/BlogHero'
import BlogGrid from '@/components/blog/BlogGrid'
// TODO: wr<PERSON><PERSON><PERSON> po buildzie
// import BlogCategories from '@/components/blog/BlogCategories'
// import BlogNewsletter from '@/components/blog/BlogNewsletter'

// Note: Tymczasowo usunięto Client Components dla SSG build
export default function BlogPage() {
  // TODO: wrócić po buildzie
  // const [activeCategory, setActiveCategory] = useState('all')

  return (
    <>
      <BlogHero />
      {/* TODO: wrócić po buildzie */}
      {/* <BlogCategories
        activeCategory={activeCategory}
        setActiveCategory={setActiveCategory}
      /> */}
      <BlogGrid activeCategory="all" />
      {/* TODO: wr<PERSON><PERSON><PERSON> po buildzie */}
      {/* <BlogNewsletter /> */}
    </>
  )
}
