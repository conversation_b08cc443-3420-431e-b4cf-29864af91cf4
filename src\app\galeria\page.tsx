// TODO: wr<PERSON><PERSON><PERSON> po buildzie - usunięto 'use client' i interaktywność
// 'use client'

// import { useState } from 'react'
import GalleryHero from '@/components/gallery/GalleryHero'
// TODO: wr<PERSON><PERSON><PERSON> po buildzie
// import GalleryFilters from '@/components/gallery/GalleryFilters'
import GalleryGrid from '@/components/gallery/GalleryGrid'
import GalleryStats from '@/components/gallery/GalleryStats'

export default function GalleryPage() {
  // TODO: wr<PERSON><PERSON><PERSON> po buildzie
  // const [activeFilter, setActiveFilter] = useState('all')
  // const [viewMode, setViewMode] = useState<'grid' | 'masonry'>('grid')

  return (
    <>
      <GalleryHero />
      <GalleryStats />
      {/* TODO: wrócić po buildzie */}
      {/* <GalleryFilters
        activeFilter={activeFilter}
        setActiveFilter={setActiveFilter}
        viewMode={viewMode}
        setViewMode={setViewMode}
      /> */}
      <GalleryGrid
        activeFilter="all"
        viewMode="grid"
      />
    </>
  )
}
