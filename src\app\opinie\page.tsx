import { Metadata } from 'next'
import ReviewsHero from '@/components/reviews/ReviewsHero'
import ReviewsStats from '@/components/reviews/ReviewsStats'
import ReviewsGrid from '@/components/reviews/ReviewsGrid'
// TODO: wró<PERSON>ć po buildzie
// import ReviewsForm from '@/components/reviews/ReviewsForm'
// import ReviewsFilters from '@/components/reviews/ReviewsFilters'

export const metadata: Metadata = {
  title: 'Opinie klientów - SOLVICTUS | Recenzje i oceny usług',
  description: 'Przeczytaj opinie zadowolonych klientów SOLVICTUS. Prawdziwe recenzje usług sprzątania po tragedii, oceny i rekomendacje.',
  keywords: 'opinie SOLVICTUS, recenzje klientów, oceny usług, rekomendacje, zadowoleni klienci, opinie o sprzątaniu',
}

export default function ReviewsPage() {
  return (
    <>
      <ReviewsHero />
      <ReviewsStats />
      {/* TODO: wr<PERSON><PERSON><PERSON> po buildzie */}
      {/* <ReviewsFilters /> */}
      <ReviewsGrid />
      {/* TODO: wr<PERSON><PERSON><PERSON> po buildzie */}
      {/* <ReviewsForm /> */}
    </>
  )
}
