import { Metadata } from 'next'
import ContactHero from '@/components/contact/ContactHero'
// TODO: wr<PERSON><PERSON><PERSON> po buildzie
// import ContactForm from '@/components/contact/ContactForm'
import ContactInfo from '@/components/contact/ContactInfo'
// TODO: wr<PERSON><PERSON><PERSON> po buildzie
// import ContactMap from '@/components/contact/ContactMap'

export const metadata: Metadata = {
  title: 'Kontakt - SOLVICTUS | Skontaktuj się z nami 24/7',
  description: 'Skontaktuj się z SOLVICTUS - linia kryzysowa 24/7, formularz kontaktowy, adres biura w Warszawie. Natychmiastowa pomoc w sytuacjach kryzysowych.',
  keywords: 'kontakt SOLVICTUS, telefon, adres, formularz kontaktowy, linia kryzysowa, Warszawa',
}

export default function ContactPage() {
  return (
    <>
      <ContactHero />
      <div className="grid grid-cols-1 lg:grid-cols-1 gap-0">
        {/* TODO: wr<PERSON><PERSON><PERSON> po buildzie */}
        {/* <ContactForm /> */}
        <ContactInfo />
      </div>
      {/* TODO: wr<PERSON><PERSON><PERSON> po buildzie */}
      {/* <ContactMap /> */}
    </>
  )
}
